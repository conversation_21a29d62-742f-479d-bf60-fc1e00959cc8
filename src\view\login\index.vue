<template>
  <div class="login-page">
    <div class="login-card">
      <div class="title">数据中心 登录</div>
      <el-form :model="form">
        <el-form-item>
          <el-input v-model="form.name" placeholder="用户名" clearable />
        </el-form-item>
        <el-form-item>
          <el-input v-model="form.pwd" placeholder="密码" show-password clearable />
        </el-form-item>
        <el-button
          type="primary"
          class="login-btn"
          color="#3C3C3C"
          :loading="loading"
          @click="handleLogin"
        >
          登录
        </el-button>
      </el-form>
    </div>
  </div>
 </template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { UserService } from "@/api";

defineOptions({
  name: "Login",
});

const router = useRouter();
const loading = ref(false);
const form = reactive({
  name: "",
  pwd: "",
});

function handleLogin() {
  if (loading.value) return;
  const name = form.name.trim();
  const pwd = form.pwd;
  if (!name) {
    ElMessage.warning("请输入用户名");
    return;
  }
  loading.value = true;
  UserService.login({ name, pwd })
    .then((res: any) => {
      loading.value = false;
      if (res?.success) {
        localStorage.setItem("isLoggedIn", "1");
        try {
          localStorage.setItem("userInfo", JSON.stringify(res.response));
        } catch (e) {}
        ElMessage.success("登录成功");
        router.replace("/");
      }
    })
    .catch(() => {
      loading.value = false;
    });
}
</script>

<style lang="scss" scoped>
.login-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: #1d1d1d;
  padding: 20px;
}

.login-card {
  width: 360px;
  padding: 30px 24px 24px;
  background: #161616;
  border: 1px solid #282828;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  color: #d8d7d5;
}

.title {
  text-align: center;
  font-size: 20px;
  margin-bottom: 18px;
}

.login-btn {
  width: 100%;
}

::v-deep(.el-input__wrapper) {
  background-color: #282828 !important;
  border: unset;
  box-shadow: none !important;
}
</style>

