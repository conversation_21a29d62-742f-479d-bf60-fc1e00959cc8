<template>
  <div class="container">
    <div class="header">
      <div class="header-left">
        <el-input class="search-input" v-model="searchValue" placeholder="搜索" clearable v-on:keydown.enter="handleQuery"
          :prefix-icon="Search" :style="{ '--el-input-bg-color': '#282828' }" />
      </div>
      <div class="header-right">
        <el-button color="#3C3C3C" :icon="Plus" @click="dialogVisible = true">
          新增
        </el-button>
      </div>
    </div>
    <div class="table">
      <el-table :data="tableData" stripe height="80vh">
        <el-table-column prop="name" label="名称" />
        <el-table-column prop="dataSourceType" label="源类型">
          <template #default="{ row }">
            {{ DataSourceType[row.dataSourceConfig?.dataSourceType] }}
          </template>
        </el-table-column>
        <el-table-column prop="identity" label="主题语句" />
        <el-table-column prop="dataSourceType" label="收/发">
          <template #default="{ row }">
            <span v-if="
              [DataSourceType.Mqtt, DataSourceType.Kafka].includes(
                row.dataSourceConfig?.dataSourceType!)
            ">
              {{ row.isPublish ? "发布" : "订阅" }}
            </span>
            <span v-else-if="
              row.dataSourceConfig?.dataSourceType ===
              DataSourceType.WebSocket
            ">
              {{ row.isPublish ? "发" : "收" }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="dataSourceConfig.asyncCache" label="是否缓存">
          <template #default="{ row }">
            <div v-if="[DataSourceType.数据库, DataSourceType.HttpApi].includes(
              row.dataSourceConfig?.dataSourceType!)">
              <el-icon v-if="row.asyncCache" size="16" color="#0F8151">
                <CircleCheckFilled />
              </el-icon>
              <el-icon v-else size="16" color="#CBA409">
                <CircleCloseFilled />
              </el-icon>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="runType" label="状态">
          <template #default="{ row }">
            <el-icon v-if="row.runType === SourceDataRunType.正常" size="16" color="#0F8151">
              <CircleCheckFilled />
            </el-icon>
            <el-icon v-else size="16" color="#CBA409">
              <CircleCloseFilled />
            </el-icon>
          </template>
        </el-table-column>
        <el-table-column prop="isOpen" label="是否开启服务">
          <template #default="{ row }">
            <span v-if="!isLiveData(row.dataSourceConfig?.dataSourceType)">-</span>
            <span v-else>
              <el-icon v-if="row.isOpen" size="16" color="#0F8151">
                <CircleCheckFilled />
              </el-icon>
              <el-icon v-else size="16" color="#CBA409">
                <CircleCloseFilled />
              </el-icon>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <template v-if="isLiveData(scope.row.dataSourceConfig?.dataSourceType)">
              <el-button type="primary" v-if="!scope.row.isOpen" :icon="Open" link color="#3C3C3C"
                @click="handleStatus(scope.row.id, 'open')" title="开启" />
              <el-button type="primary" v-else :icon="Close" link color="#3C3C3C"
                @click="handleStatus(scope.row.id, 'close')" title="关闭" />
            </template>
            <el-button type="primary" :icon="Edit" link color="#3C3C3C" @click="handleEdit(scope.row.id)" title="编辑" />
            <el-button type="primary" :icon="Delete" link color="#3C3C3C" @click="handleDelete(scope.row.id)"
              title="删除" />
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
        :page-sizes="pagination.pageSizes" layout="total, sizes, prev, pager, next, jumper" :total="pagination.total"
        @size-change="handleQuery" @current-change="handleQuery" />
    </div>
    <data-source-data-dialog v-if="dialogVisible" ref="dialogRef" v-model="dialogVisible" :id="editId" />
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, nextTick, onMounted, reactive, ref, watch } from "vue";
import {
  Search,
  Edit,
  Delete,
  Plus,
  CircleCheckFilled,
  CircleCloseFilled,
  Close,
  Open,
} from "@element-plus/icons-vue";
import { DataSourceDataService } from "@/api";
import { DataSource, DataSourceType, SourceDataRunType } from "@/models";
import DataSourceDataDialog from "./data-source-data-dialog.vue";
import { ElMessage } from "element-plus";

defineOptions({
  name: "DataSourceData",
});

const searchValue = ref("");
const tableData = ref<DataSource[]>([]);
const dialogVisible = ref(false);
const instance = getCurrentInstance();
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0,
  pageSizes: [20, 30, 50, 100],
});

const editId = ref<number | undefined>(undefined);

watch(dialogVisible, (newVal) => {
  if (!newVal) {
    editId.value = undefined;
    handleQuery();
  }
});

const handleQuery = () => {
  DataSourceDataService.getList({
    page: pagination.currentPage,
    pageSize: pagination.pageSize,
    key: searchValue.value,
  }).then((res) => {
    tableData.value = res.response.data;
    pagination.total = res.response.dataCount;
  });
};

const handleEdit = (id: number) => {
  dialogVisible.value = true;
  editId.value = id;
  nextTick(() => {
    console.log(instance);
  });
};

const handleDelete = (id: number) => {
  DataSourceDataService.delete(id).then((res) => {
    if (res.success) {
      ElMessage.success("删除成功");
      handleQuery();
    }
  });
};

const handleStatus = (id: number, status: "open" | "close") => {
  if (status === 'close') {
    DataSourceDataService.close(id).then((res) => {
      if (res.success) {
        ElMessage.success("关闭成功");
        handleQuery();
      }
    });
  } else {
    DataSourceDataService.open(id).then((res) => {
      if (res.success) {
        ElMessage.success("开启成功");
        handleQuery();
      }
    });
  }
};



const isLiveData = (status: DataSourceType | undefined) => {
  return [DataSourceType.Mqtt, DataSourceType.WebSocket, DataSourceType.Kafka].includes(status ?? DataSourceType.数据库);
};

onMounted(() => {
  handleQuery();
});
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 10px;
  background-color: #2b2b2b;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 47px;
    padding: 0 24px;

    .search-input {
      :deep(.el-input__wrapper) {
        background-color: #282828;
        box-shadow: none !important;
        border: none;
      }
    }
  }
}
</style>
