<template>
  <div class="header">
    <div class="header-left">
      <b class="header-title">数据中心</b>
    </div>
    <el-menu :default-active="activeIndex" class="header-menu" router mode="horizontal">
      <el-menu-item v-for="menu in menus" :index="menu.path">{{ menu.meta?.title }}
      </el-menu-item>
    </el-menu>
    <div class="header-right">
      <span class="user-name">{{ userName }}</span>
      <el-button size="small" text type="primary" @click="logout">退出</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useMneuStoreHook } from "@/store/menu";
import { ref } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();

defineOptions({
  name: "NHeader",
});


// 使用父级路径作为激活菜单项
const activeIndex = router.currentRoute.value.path;

const userName = ref("");

const menus = useMneuStoreHook().router;


function logout() {
  localStorage.removeItem("isLoggedIn");
  localStorage.removeItem("userInfo");
  router.replace("/login");
}
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 54px;
  padding: 12px 40px;
  gap: 55px;
  border-bottom: 1px solid #282828;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  background: #161616;

  .header-left {
    display: flex;
    align-items: center;

    .header-title {
      color: #d8d7d5;
      font-family: "Gilroy";
      font-size: 25px;
      font-style: italic;
      font-weight: Bold Italic;
      line-height: normal;
    }
  }

  .header-menu {
    margin-left: 10px;
    flex: 1;
    align-items: center;
    min-width: 0;
    height: 100%;
  }

  .header-right {
    display: flex;
    align-items: center;
  }
}

.el-menu--horizontal.el-menu {
  border: none;
}
</style>
