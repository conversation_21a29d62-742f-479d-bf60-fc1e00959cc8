<template>
  <div class="data-management-container">
    <template v-if="showTable">
      <div class="data-management-header">
        <div class="data-management-header-left">
          <el-input class="search-input" v-model="searchValue" placeholder="搜索" clearable
            v-on:keydown.enter="handleQuery" :prefix-icon="Search" />
        </div>
        <div class="data-management-header-right">
          <el-button color="#3C3C3C" :icon="Plus" @click="dataManagementDialogVisible = true">
            新增
          </el-button>
        </div>
      </div>
      <div class="data-management-table">
        <el-table :data="tableData" stripe height="80vh">
          <el-table-column prop="name" label="名称" />
          <el-table-column prop="remark" label="备注" />
          <el-table-column prop="isEnable" label="状态">
            <template #default="{ row }">
              <el-icon v-if="row.isEnable" size="16" color="#0F8151">
                <CircleCheckFilled />
              </el-icon>
              <el-icon v-else size="16" color="#CBA409">
                <CircleCloseFilled />
              </el-icon>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <el-button type="primary" v-if="scope.row.isEnable" :icon="Close" link color="#3C3C3C"
                @click="handleStatus(scope.row.id, 'close')" title="关闭" />
              <el-button type="primary" v-else :icon="Open" link color="#3C3C3C"
                @click="handleStatus(scope.row.id, 'open')" title="开启" />

              <el-button type="primary" link color="#3C3C3C" :icon="Link" @click="handleLink(scope.row.id)" />
              <el-button type="primary" :icon="Edit" link color="#3C3C3C" @click="handleEdit(scope.row.id)" />
              <el-button type="primary" :icon="Delete" link color="#3C3C3C" @click="handleDelete(scope.row.id)" />
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
          :page-sizes="pagination.pageSizes" layout="total, sizes, prev, pager, next, jumper" :total="pagination.total"
          @size-change="handleQuery" @current-change="handleQuery" />
      </div>
      <data-management-dialog :id="editId" v-model="dataManagementDialogVisible" v-if="dataManagementDialogVisible" />
    </template>
    <template v-else>
      <div class="data-management-iframe-container">
        <iframe ref="iframeRef" style="
            width: 100%;
            height: 100%;
            top: 0px;
            bottom: 0px;
            border: none;
            position: absolute;
          " @load="handleLoad" src="./webapp/index.html"></iframe>
        <div class="data-management-iframe-container-button">
          <el-button @click="showTable = true">返回</el-button>
          <el-button @click="clearData">清空</el-button>
          <el-button @click="handleSave">保存</el-button>
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import {
  DataInterfaceService,
  DataManagementService,
  DataSourceDataService,
  ProcessLibraryService,
} from "@/api";
import AggregationSelectionDialog from "@/components/dialog/AggregationSelectionDialog.vue";
import DataInterfaceSelectionDialog from "@/components/dialog/DataInterfaceSelectionDialog.vue";
import DataProcessingDialog from "@/components/dialog/DataProcessingDialog.vue";
import DataSourceSelectionDialog from "@/components/dialog/DataSourceSelectionDialog.vue";
import ProcessLibrarySelectionDialog from "@/components/dialog/ProcessLibrarySelectionDialog.vue";
import RuleSelectionDialog from "@/components/dialog/RuleSelectionDialog.vue";
import {
  DataInterfaceType,
  DataManagement,
  DataSourceType,
} from "@/models";
import { FieldInputType, FieldType, showComponentDialog, showFieldInputDialog as showCustomFieldInputDialog } from "@/services/dialog.service";
import { CircleCheckFilled, CircleCloseFilled, Close, Delete, Edit, Link, Open, Plus, Search } from "@element-plus/icons-vue";
import {
  ElButton,
  ElInput,
  ElMessage,
} from "element-plus";
import {
  onBeforeUnmount,
  onMounted,
  reactive,
  ref,
  useTemplateRef,
  watch,
} from "vue";
import DataManagementDialog from "./data-management-dialog.vue";

defineOptions({
  name: "data-management",
});

const showTable = ref(true);
const tableData = ref<DataManagement[]>([]);
const dataManagementDialogVisible = ref(false);
const iframeRef = useTemplateRef("iframeRef");
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0,
  pageSizes: [20, 30, 50, 100],
});

const editId = ref<number | undefined>(undefined);

const searchValue = ref("");

watch(dataManagementDialogVisible, (newVal) => {
  if (!newVal) {
    editId.value = undefined;
    handleQuery();
  }
});

const handleLink = (id: number) => {
  showTable.value = false;
  editId.value = id;
};

const handleEdit = (id: number) => {
  editId.value = id;
  dataManagementDialogVisible.value = true;
};

const handleDelete = (id: number) => {
  DataManagementService.delete(id).then((res) => {
    if (res.success) {
      ElMessage.success("删除成功");
      handleQuery();
    }
  });
};

const handleQuery = () => {
  DataManagementService.getList({
    page: pagination.currentPage,
    pageSize: pagination.pageSize,
    key: searchValue.value,
  }).then((res) => {
    tableData.value = res.response.data;
    pagination.total = res.response.dataCount;
  });
};

const handleStatus = (id: number, status: 'open' | 'close') => {
  if (status === "open") {
    DataManagementService.open(id).then((res) => {
      if (res.success) {
        ElMessage.success("开启成功");
        handleQuery();
      }
    });
  } else {
    DataManagementService.close(id).then((res) => {
      if (res.success) {
        ElMessage.success("关闭成功");
        handleQuery();
      }
    });
  }
};

const handleLoad = () => {
  const data = tableData.value.find((item) => item.id === editId.value);
  if (data && data.xmlConfig) {
    (iframeRef.value?.contentWindow as any)?.Zoogo.ui.setFileData(
      data.xmlConfig
    );
  }
};

const handleSave = async () => {
  const config = (iframeRef.value?.contentWindow as any)?.Zoogo?.getETLJson();
  const xmlConfig = (
    iframeRef.value?.contentWindow as any
  )?.Zoogo?.file.createData();
  if (!editId.value) return;
  const dataManagement = await DataManagementService.getById(editId.value);
  DataManagementService.update({
    ...dataManagement,
    config,
    xmlConfig: xmlConfig,
  }).then((res) => {
    if (res.success) {
      ElMessage.success("保存成功");
      showTable.value = true;
      editId.value = undefined;
      handleQuery();
    }
  });
};

const clearData = () => {
  (iframeRef.value?.contentWindow as any)?.Zoogo.ui.setFileData("");
};

// 定义类型枚举
enum SourceType {
  源模型 = "源模型",
  Api = "Api",
  MQTT = "MQTT",
  Socket = "Socket",
  Kafka = "Kafka",
  目标模型 = "目标模型",
  目标Api = "目标Api",
  目标MQTT = "目标MQTT",
  目标Socket = "目标Socket",
}

// 数据源类型映射
const sourceTypeMap = {
  [SourceType.源模型]: DataSourceType.数据库,
  [SourceType.Api]: DataSourceType.HttpApi,
  [SourceType.MQTT]: DataSourceType.Mqtt,
  [SourceType.Socket]: DataSourceType.WebSocket,
  [SourceType.Kafka]: DataSourceType.Kafka,
};

const targetInterfaceTypeMap = {
  [SourceType.目标Api]: {
    interfaceType: DataInterfaceType.API,
    sourceType: DataSourceType.HttpApi,
  },
  [SourceType.目标MQTT]: {
    interfaceType: DataInterfaceType.MQTT,
    sourceType: DataSourceType.Mqtt,
  },
  [SourceType.目标Socket]: {
    interfaceType: DataInterfaceType.WebSocket,
    sourceType: DataSourceType.WebSocket,
  },
};

const fieldTypes: FieldType[] = [
  { label: '字符串', value: 'string', inputType: FieldInputType.常量, placeholder: '请输入字符串值', defaultValue: '' },
  { label: '时间', value: 'datetime', inputType: FieldInputType.时间, placeholder: '请选择时间', defaultValue: '$TIME_now_"yyyy-MM-dd HH:mm:ss"' },
  { label: 'GUID', value: 'guid', inputType: FieldInputType.GUID, placeholder: 'GUID将自动生成', defaultValue: '$GUID' },
];



// 新的字段输入对话框
async function showFieldInputDialog() {
  const data = await showCustomFieldInputDialog(fieldTypes);
  return data;
}

async function linkFunction(type: string, data: any) {
  let result;

  // 处理数据源类型
  if (type in sourceTypeMap) {
    const dataSourceType = sourceTypeMap[type as keyof typeof sourceTypeMap];
    const res = await DataSourceDataService.getDataByType(dataSourceType);

    try {
      result = await showComponentDialog(
        DataSourceSelectionDialog,
        { data: res.response, type },
        {
          title: "选择数据源",
          width: "900px"
        }
      );
    } catch (error) {
      console.error("数据源选择被取消", error);
      return data;
    }
  }

  // 处理目标模型
  else if (type === SourceType.目标模型) {
    const res = await ProcessLibraryService.all();

    try {
      result = await showComponentDialog(
        ProcessLibrarySelectionDialog,
        { data: res.response },
        {
          title: "选择目标模型",
          width: "800px"
        }
      );
    } catch (error) {
      console.error("目标模型选择被取消", error);
      return data;
    }
  }

  // 处理目标接口类型
  else if (type in targetInterfaceTypeMap) {
    const typeConfig =
      targetInterfaceTypeMap[type as keyof typeof targetInterfaceTypeMap];
    const res = await DataInterfaceService.getDataByType(
      typeConfig.interfaceType
    );

    try {
      result = await showComponentDialog(
        DataInterfaceSelectionDialog,
        { data: res.response, sourceType: typeConfig.sourceType },
        {
          title: "选择目标接口",
          width: "900px"
        }
      );
    } catch (error) {
      console.error("目标接口选择被取消", error);
      return data;
    }
  } else if (type === "数据处理") {
    try {
      result = await showComponentDialog(
        DataProcessingDialog,
        {
          config: data.config,
          inputs: data.inputs,
          bodys: data.bodys
        },
        {
          title: "数据处理",
          width: "1000px",
          height: "650px",
          top: '5vh'
        }
      )
    } catch (error) {
      console.error("数据处理输入被取消", error);
      return data;
    }
  } else if (type === "字段") {
    result = await showFieldInputDialog();
  } else if (type === "清洗组件" || type === "数据脱敏") {
    try {
      result = await showComponentDialog(
        RuleSelectionDialog,
        { ruleType: type as '清洗组件' | '数据脱敏' },
        {
          title: `选择${type}规则`,
          width: '600px'
        }
      );
    } catch (error) {
      console.error("操作被取消", error);
      return data;
    }
  } else if (type === "聚合函数") {
    try {
      result = await showComponentDialog(
        AggregationSelectionDialog,
        {},
        {
          title: '选择聚合函数',
          width: '500px'
        }
      );
    } catch (error) {
      console.error("聚合函数选择被取消", error);
    }
  }
  return result ?? data;
}

onMounted(() => {
  handleQuery();
  (window as any).linkFun = linkFunction;
});

onBeforeUnmount(() => {
  (window as any).linkFun = undefined;
});
</script>

<style lang="scss" scoped>
.data-management-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 10px;
  background-color: #2b2b2b;

  .data-management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 47px;
    padding: 0 24px;
  }
}

.data-management-iframe-container {
  .data-management-iframe-container-button {
    position: absolute;
    top: 7px;
    right: 7px;
  }
}
</style>
