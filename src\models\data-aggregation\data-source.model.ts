import { BaseResource } from "../system.model";

export interface DataSource extends BaseResource {
  name?: string;
  dataSourceType?: DataSourceType;
  detailConfig?: DetailConfigType;
  version?: number;
  remark?: string;
}

/**Mqtt协议版本 */
export enum MqttProtocolVersion {
  Unknown = 0,
  V310 = 3,
  V311 = 4,
  V500 = 5,
}


/**数据源类型 */
export enum DataSourceType {
  数据库 = 1,
  HttpApi = 2,
  WebSocket = 3,
  Mqtt = 4,
  //Socket = 6,
  Kafka = 5,
}

export enum DbType {
  MySql = 0,
  SqlServer = 1,
  PostgreSql = 2,
  TimescaleDB = 3,
}

/**数据库配置 */
export interface DatabaseConfig {
  /**数据库类型 */
  dbType?: DbType;
  /**ip */
  ip?: string;
  /**端口 */
  port?: number;
  /**数据库名称 */
  dbName?: string;
  /**用户名 */
  userName?: string;
  /**密码 */
  password?: string;

  //** 链接字符串 */
  connectionString?:string;
}
/**HttpApi配置 */
export interface HttpApiConfig {
  /**请求地址 */
  requestUrl?: string;
  /**请求方法 */
  method?: HttpMethod;
  /**请求参数 */
  params?: { [key: string]: string };
  /**请求头 */
  headers?: { [key: string]: string };
  /**是否Token认证 */
  isTokenAuthorize?: number;
  /**登录地址（Token认证时有效） */
  loginUrl?: string;
  /**用户名（Token认证时有效） */
  userName?: string;
  /**密码（Token认证时有效） */
  password?: string;
  /**请求体（Token认证时有效） */
  body?: string;
  /**授权方式（Token认证时有效） */
  tokenAuthorizeType?: TokenAuthorizeType;
  /**有效时间（Token认证时有效） */
  intervalTime?: number;
  /**key名称（Token认证时有效） */
  keyNamePath?: string;
  /**token名称（Token认证时有效） */
  tokenName?: string;
}

/**token授权类型 */
export enum TokenAuthorizeType {
  JWT = 0,
  其它 = 5,
}

/**WebSocket配置 */
export interface WebSocketConfig {
  /**ws/wss地址 */
  url?: string;
}
/**Mqtt配置 */
export interface MqttConfig {
  /**地址 */
  url?: string;
  /**端口 */
  port?: number;
  /**主题 */
  topic?: string;
  /**用户名 */
  userName?: string;
  /**密码 */
  password?: string;

  /**协议版本 */
  protocolVersion?: MqttProtocolVersion;
}
/**Kafka配置 */
export interface KafkaConfig {
  /**支持多集群地址 */
  address?: string;
  /**用户名 */
  userName?: string;
  /**密码 */
  password?: string;
  /**SASL机制 */
  saslMechanism?: SaslMechanism;
  /**安全协议 */
  securityProtocol?: SecurityProtocol;
  /**消费偏移 */
  autoOffsetReset?: AutoOffsetReset;
}

/**Http请求方法 */
export enum HttpMethod {
  Get = 0,
  Post,
  Put,
  Delete,
  Head,
  Options,
  Patch,
  Merge,
  Copy,
  Search,
}

export interface HeaderItem {
  key: string;
  value: string;
}

/**数据源详细配置 */
export type DetailConfigType = {
  [DataSourceType.数据库]: DatabaseConfig;
  [DataSourceType.HttpApi]: HttpApiConfig;
  [DataSourceType.WebSocket]: WebSocketConfig;
  [DataSourceType.Mqtt]: MqttConfig;
  [DataSourceType.Kafka]: KafkaConfig;
}[DataSourceType];

export enum SaslMechanism {
  //
  // 摘要:
  //     GSSAPI
  Gssapi,
  //
  // 摘要:
  //     PLAIN
  Plain,
  //
  // 摘要:
  //     SCRAM-SHA-256
  ScramSha256,
  //
  // 摘要:
  //     SCRAM-SHA-512
  ScramSha512,
  //
  // 摘要:
  //     OAUTHBEARER
  OAuthBearer,
}
export enum SecurityProtocol {
  //
  // 摘要:
  //     Plaintext
  Plaintext,
  //
  // 摘要:
  //     Ssl
  Ssl,
  //
  // 摘要:
  //     SaslPlaintext
  SaslPlaintext,
  //
  // 摘要:
  //     SaslSsl
  SaslSsl,
}

export enum AutoOffsetReset {
  //
  // 摘要:
  //     Latest
  Latest,
  //
  // 摘要:
  //     Earliest
  Earliest,
  //
  // 摘要:
  //     Error
  Error
}
