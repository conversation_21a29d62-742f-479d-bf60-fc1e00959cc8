<template>
  <div class="user-container">
    <div class="user-header">
      <div class="user-header-left">
        <el-input v-model="searchValue" placeholder="搜索" clearable :prefix-icon="Search" class="search-input"
          v-on:keydown.enter="handleQuery" @clear="handleQuery" />
      </div>
      <div class="user-header-right">
        <el-button color="#3C3C3C" :icon="Plus" @click="openAddDialog">新增</el-button>
      </div>
    </div>

    <div class="user-table">
      <el-table :data="pagedData" stripe height="80vh">
        <el-table-column prop="userDomainName" label="用户名称" />
        <el-table-column label="操作" width="160" fixed="right">
          <template #default="scope">
            <el-button type="primary" :icon="Edit" link color="#3C3C3C" @click="openEditDialog(scope.row)" />
            <el-button type="primary" :icon="Delete" link color="#3C3C3C" @click="handleDelete(scope.row.id)" />
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="pagination">
      <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
        :page-sizes="pagination.pageSizes" layout="total, sizes, prev, pager, next, jumper" :total="pagination.total"
        @size-change="recomputePage" @current-change="recomputePage" />
    </div>

    <user-dialog v-if="addDialogVisible" v-model="addDialogVisible" :id="editId" @success="handleQuery" />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, computed, watch } from "vue";
import { Plus, Delete, Edit, Search } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { User, ListResponse } from "@/models";
import { UserService } from "@/api";
import UserDialog from "./user-dialog.vue";

defineOptions({
  name: "UserList",
});

const tableData = ref<User[]>([]);
const searchValue = ref("");
const addDialogVisible = ref(false);

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0,
  pageSizes: [10, 20, 30, 50, 100],
});

const pagedData = computed(() => tableData.value);

function recomputePage() {
  handleQuery();
}

const editId = ref<number | undefined>(undefined);

function openAddDialog() {
  editId.value = undefined;
  addDialogVisible.value = true;
}

function handleDelete(id?: number) {
  if (id == null) return;
  UserService.delete(id).then((res) => {
    if (res.success) {
      ElMessage.success("删除成功");
      handleQuery();
    }
  });
}

function openEditDialog(row: User) {
  editId.value = row.id;
  addDialogVisible.value = true;
}

function handleQuery() {
  UserService.getList({
    page: pagination.currentPage,
    pageSize: pagination.pageSize,
    name: searchValue.value,
  }).then((res) => {
    const list = (res as unknown as ListResponse<User>).response;
    tableData.value = list.data;
    pagination.total = list.dataCount;
  });
}

watch(
  () => [pagination.currentPage, pagination.pageSize],
  () => handleQuery()
);

onMounted(() => {
  handleQuery();
});

watch(addDialogVisible, (newVal) => {
  if (!newVal) {
    editId.value = undefined;
    handleQuery();
  }
});
</script>

<style lang="scss" scoped>
.user-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 10px;
  background-color: #2b2b2b;

  .user-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 47px;
    padding: 0 24px;

    .search-input {
      width: 260px;
    }
  }
}

.user-table {
  padding: 0 10px;
}

.pagination {
  display: flex;
  justify-content: flex-end;
  padding: 0 24px 10px;
}
</style>
