import { useMneuStoreHook } from "@/store/menu";
import type { RouteRecordRaw } from "vue-router";
import { createRouter, createWebHistory } from "vue-router";
const Layout = () => import("../layout/index.vue");

const routes: RouteRecordRaw[] = [
  {
    path: "/login",
    name: "Login",
    meta: {
      hidden: true,
    },
    component: () => import("../view/login/index.vue"),
  },
  {
    path: "/",
    component: Layout,
    meta: {
      title: "数据汇聚",
      rank: 0,
    },
    name: "Home",
    children: [
      {
        path: "/",
        name: "数据源",
        meta: {
          title: "数据源",
        },
        component: () =>
          import("../view/data-aggregation/data-source/index.vue"),
      },
      {
        path: "/data-source-data",
        name: "数据源数据",
        meta: {
          title: "数据源数据",
        },
        component: () =>
          import("../view/data-aggregation/data-source-data/index.vue"),
      },
      {
        path: "/process-library",
        name: "过程库",
        meta: {
          title: "过程库",
        },
        component: () =>
          import("../view/data-aggregation/process-library/index.vue"),
      },
      {
        path: "/data-interface-subject",
        name: "数据接口主题",
        meta: {
          title: "数据接口主题",
        },
        component: () =>
          import("../view/data-aggregation/data-interface-subject/index.vue"),
      },
      {
        path: "/data-interface",
        name: "数据接口",
        meta: {
          title: "数据接口",
        },
        component: () =>
          import("../view/data-aggregation/data-interface/index.vue"),
      },
      {
        path: "/data-management",
        name: "数据治理",
        meta: {
          title: "数据治理",
        },
        component: () =>
          import("../view/data-aggregation/data-management/index.vue"),
      },
      {
        path: "/user",
        name: "用户管理",
        meta: {
          title: "用户管理",
        },
        component: () => import("../view/user/index.vue"),
      },
    ],
  },
  // {
  //   path: "/data-collection",
  //   component: Layout,
  //   meta: {
  //     title: "数据采集",
  //     rank: 1,
  //   },
  //   name: "DataCollection",
  //   redirect: "/data-collection/iot-gateway",
  //   children: [
  //     {
  //       path: "/data-collection/iot-gateway",
  //       name: "物联网关",
  //       meta: {
  //         title: "物联网关",
  //       },
  //       component: () =>
  //         import("../view/data-collection/iot-gateway/index.vue"),
  //     },
  //   ],
  // },
  // {
  //   path: "/preview",
  //   name: "Preview",
  //   meta: {
  //     title: "预览",
  //   },
  //   component: Layout,
  //   children: [
  //     {
  //       path: "/preview",
  //       name: "Preview",
  //       meta: {
  //         title: "预览",
  //       },
  //       component: () => import("../view/preview/index.vue"),
  //     },
  //   ],
  // },
];
useMneuStoreHook().routesToMenu(routes);

export const router = createRouter({
  history: createWebHistory(),
  routes: routes,
});

// 简单鉴权：localStorage 中 `isLoggedIn` 为 "1" 视为已登录
router.beforeEach((to, _from, next) => {
  const isLoggedIn = localStorage.getItem("isLoggedIn") === "1";
  if (to.path === "/login") {
    if (isLoggedIn) return next("/");
    return next();
  }
  if (!isLoggedIn) return next({ path: "/login" });
  next();
});
