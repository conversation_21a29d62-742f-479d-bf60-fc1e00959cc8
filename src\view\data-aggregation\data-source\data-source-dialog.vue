<template>
  <div class="data-source-dialog-container">
    <el-dialog v-model="visible" :title="isEdit ? '编辑数据源' : '新增数据源'" width="40%" align-center>
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px" label-position="top">
        <el-row :gutter="24" justify="space-between">
          <el-col :span="12">
            <el-form-item label="名称 :" prop="name" required>
              <el-input v-model="formData.name" placeholder="请输入名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类型 :" prop="dataSourceType">
              <el-select class="el-select" v-model="formData.dataSourceType" placeholder="请选择类型"
                @change="handleDataSourceTypeChange">
                <el-option v-for="(item, key) in enumToObject(DataSourceType)" :key="key" :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <template v-if="formData.dataSourceType === DataSourceType.数据库">
            <el-col :span="12">
              <el-form-item label="数据库类型 :" prop="detailConfig.dbType">
                <el-select v-model="(formData.detailConfig as DatabaseConfig).dbType" placeholder="请选择数据库类型">
                  <el-option v-for="(item, key) in enumToObject(DbType)" :key="key" :label="item.label"
                    :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="Ip :" required prop="detailConfig.ip">
                <el-input v-model="(formData.detailConfig as DatabaseConfig).ip" placeholder="请输入Ip地址" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="端口 :" required prop="detailConfig.port">
                <el-input v-model.number="(formData.detailConfig as DatabaseConfig).port" placeholder="请输入端口"
                  clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="数据库名称 :" required prop="detailConfig.dbName">
                <el-input v-model="(formData.detailConfig as DatabaseConfig).dbName" placeholder="请输入数据库名称" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="用户名 :" required prop="detailConfig.userName">
                <el-input v-model="(formData.detailConfig as DatabaseConfig).userName" placeholder="请输入用户名" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="密码 :" required prop="detailConfig.password">
                <el-input v-model="(formData.detailConfig as DatabaseConfig).password" placeholder="请输入密码" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="链接字符串 :" prop="detailConfig.connectionString">
                <el-input v-model="(formData.detailConfig as DatabaseConfig).connectionString" placeholder="请输入链接字符串"
                  clearable />
              </el-form-item>
            </el-col>
          </template>
          <template v-else-if="formData.dataSourceType === DataSourceType.HttpApi">
            <el-col :span="24">
              <el-form-item label="请求地址 :" required prop="detailConfig.requestUrl">
                <el-input v-model="(formData.detailConfig as HttpApiConfig).requestUrl" placeholder="请输入请求地址"
                  clearable />
              </el-form-item>
            </el-col>
            <el-col :span="24">

              <el-form-item label="认证方式 :">
                <el-radio-group v-model="(formData.detailConfig as HttpApiConfig).isTokenAuthorize" :default-value="0">
                  <el-radio :label="0">Header认证</el-radio>
                  <el-radio :label="1">Token认证</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <!-- Token认证配置 -->
            <template v-if="(formData.detailConfig as HttpApiConfig).isTokenAuthorize === 1">
              <el-col :span="12">
                <el-form-item label="登录地址 :" prop="detailConfig.loginUrl">
                  <el-input v-model="(formData.detailConfig as HttpApiConfig).loginUrl" placeholder="请输入登录地址"
                    clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="请求方式 :" required prop="detailConfig.method">
                  <el-select class="el-select" v-model="(formData.detailConfig as HttpApiConfig).method"
                    placeholder="请选择请求方式">
                    <el-option v-for="item in methodList" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="Body :" prop="detailConfig.body">
                  <el-input v-model="(formData.detailConfig as HttpApiConfig).body" type="textarea" :rows="4"
                    placeholder="请输入Body内容" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="授权方式 :" prop="tokenAuthorizeType">
                  <el-select v-model="(formData.detailConfig as HttpApiConfig).tokenAuthorizeType"
                    placeholder="请选择授权方式">
                    <el-option v-for="item in enumToObject(TokenAuthorizeType)" :key="item.value" :label="item.label"
                      :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="有效时间(分钟) :" prop="detailConfig.intervalTime">
                  <el-input v-model.number="(formData.detailConfig as HttpApiConfig).intervalTime" :min="1"
                    placeholder="请输入有效时间" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="key名称 :" prop="detailConfig.keyNamePath">
                  <el-input v-model="(formData.detailConfig as HttpApiConfig).keyNamePath" placeholder="请输入key名称"
                    clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="token名称 :" prop="detailConfig.tokenName">
                  <el-input v-model="(formData.detailConfig as HttpApiConfig).tokenName" placeholder="请输入token名称"
                    clearable />
                </el-form-item>
              </el-col>
            </template>

            <!-- Header认证配置 -->
            <template v-else>
              <el-col :span="24">
                <el-form-item label="请求头 :">
                  <div class="header-list">
                    <div v-for="(item, index) in headerList" :key="index" class="header-item">
                      <el-input v-model="item.key" placeholder="Header键" @change="updateHeaders" class="header-key" />
                      <el-input v-model="item.value" placeholder="Header值" @change="updateHeaders"
                        class="header-value" />
                      <el-button @click="removeHeader(index)" type="danger" :icon="Delete" size="small"
                        class="header-delete-btn" />
                    </div>
                  </div>
                  <div class="add-header">
                    <el-button @click="addHeader" type="primary" size="small" class="header-add-btn">
                      <el-icon class="el-icon--left">
                        <Plus />
                      </el-icon>
                    </el-button>
                  </div>
                </el-form-item>
              </el-col>
            </template>
          </template>
          <template v-else-if="formData.dataSourceType === DataSourceType.WebSocket">
            <el-col :span="24">
              <el-form-item label="连接地址 :" required prop="detailConfig.url">
                <el-input v-model="(formData.detailConfig as WebSocketConfig).url" placeholder="请输入连接地址" clearable />
              </el-form-item>
            </el-col>
          </template>
          <template v-else-if="formData.dataSourceType === DataSourceType.Mqtt">
            <el-col :span="12">
              <el-form-item label="协议版本 :" prop="detailConfig.protocolVersion" required>
                <el-select v-model="(formData.detailConfig as MqttConfig).protocolVersion" placeholder="请选择协议版本">
                  <el-option v-for="(item, key) in enumToObject(MqttProtocolVersion)" :key="key" :label="item.label"
                    :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="连接地址 :" required prop="detailConfig.url">
                <el-input v-model="(formData.detailConfig as MqttConfig).url" placeholder="请输入连接地址" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="端口 :" prop="detailConfig.port">
                <el-input v-model.number="(formData.detailConfig as MqttConfig).port" placeholder="请输入端口" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="用户名 :" prop="detailConfig.userName" :required="false">
                <el-input v-model="(formData.detailConfig as MqttConfig).userName" placeholder="请输入用户名" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="密码 :" prop="detailConfig.password" :required="false">
                <el-input v-model="(formData.detailConfig as MqttConfig).password" placeholder="请输入密码" clearable />
              </el-form-item>
            </el-col>
          </template>
          <template v-else-if="formData.dataSourceType === DataSourceType.Kafka">
            <el-col :span="24">
              <el-form-item label="连接地址 :" required prop="detailConfig.address">
                <el-input v-model="(formData.detailConfig as KafkaConfig).address" placeholder="请输入连接地址（支持多集群地址）"
                  clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="用户名 :" prop="detailConfig.userName" :required="false">
                <el-input v-model="(formData.detailConfig as KafkaConfig).userName" placeholder="请输入用户名" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="密码 :" prop="detailConfig.password" :required="false">
                <el-input v-model="(formData.detailConfig as KafkaConfig).password" placeholder="请输入密码" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="SASL机制 :" prop="detailConfig.saslMechanism">
                <el-select v-model="(formData.detailConfig as KafkaConfig).saslMechanism" placeholder="请选择SASL机制"
                  clearable>
                  <el-option v-for="(item, key) in enumToObject(SaslMechanism)" :key="key" :label="item.label"
                    :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="安全协议 :" prop="detailConfig.securityProtocol">
                <el-select v-model="(formData.detailConfig as KafkaConfig).securityProtocol" placeholder="请选择安全协议"
                  clearable>
                  <el-option v-for="(item, key) in enumToObject(SecurityProtocol)" :key="key" :label="item.label"
                    :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="消费偏移 :" prop="detailConfig.autoOffsetReset">
                <el-select v-model="(formData.detailConfig as KafkaConfig).autoOffsetReset" placeholder="请选择消费偏移"
                  clearable>
                  <el-option v-for="(item, key) in enumToObject(AutoOffsetReset)" :key="key" :label="item.label"
                    :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="24">
            <el-form-item label="备注 :" prop="remark">
              <el-input v-model="formData.remark" type="textarea" :rows="4" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { DataSourceService } from "@/api";
import {
  AutoOffsetReset,
  DatabaseConfig,
  DataSource,
  DataSourceType,
  DbType,
  HeaderItem,
  HttpApiConfig,
  HttpMethod,
  KafkaConfig,
  MqttConfig,
  MqttProtocolVersion,
  SaslMechanism,
  SecurityProtocol,
  TokenAuthorizeType,
  WebSocketConfig,
} from "@/models";
import { enumToObject } from "@/utils/enum";
import { Delete, Plus } from '@element-plus/icons-vue';
import { ElForm, ElMessage, FormRules, type FormInstance } from "element-plus";
import { onMounted, reactive, ref } from "vue";

defineOptions({
  name: "DataSourceDialog",
});

let formData = ref<DataSource>({
  dataSourceType: DataSourceType.数据库,
  id: undefined,
  name: "",
  detailConfig: {
    dbType: DbType.MySql,
    dbName: "",
    tokenAuthorizeType: TokenAuthorizeType.JWT,
  },
});

const methodList = enumToObject(HttpMethod);

// 初始化时设置默认值
const initFormData = () => {
  if (formData.value.dataSourceType === DataSourceType.HttpApi) {
    // 设置HttpApi默认值
    const httpConfig = formData.value.detailConfig as HttpApiConfig;
    if (httpConfig.isTokenAuthorize === undefined) {
      httpConfig.isTokenAuthorize = 0; // 默认使用Header认证
    }
    if (httpConfig.tokenAuthorizeType === undefined) {
      httpConfig.tokenAuthorizeType = TokenAuthorizeType.JWT;
    }
    if (!httpConfig.headers) {
      httpConfig.headers = {};
    }
    if (httpConfig.body === undefined) {
      httpConfig.body = "";
    }

    // 初始化headerList
    headerList.value = [];
    if (httpConfig.headers) {
      for (const [key, value] of Object.entries(httpConfig.headers)) {
        headerList.value.push({ key, value });
      }
    }
  }
};

let formRef = ref<FormInstance>();

// 保存header的键名，用于双向绑定
const headerList = ref<HeaderItem[]>([]);

// 添加新的header
const addHeader = () => {
  const httpConfig = formData.value.detailConfig as HttpApiConfig;
  if (!httpConfig.headers) {
    httpConfig.headers = {};
  }

  headerList.value.push({ key: '', value: '' });
};

// 更新headers
const updateHeaders = () => {
  const httpConfig = formData.value.detailConfig as HttpApiConfig;
  if (!httpConfig.headers) {
    httpConfig.headers = {};
  }

  // 清空现有headers
  Object.keys(httpConfig.headers).forEach(key => {
    delete httpConfig.headers![key];
  });

  // 添加新的headers
  headerList.value.forEach(item => {
    if (item.key) {
      httpConfig.headers![item.key] = item.value || '';
    }
  });
};

// 删除header
const removeHeader = (index: number) => {
  headerList.value.splice(index, 1);
  updateHeaders();
};

const rules = reactive<FormRules>({
  name: [{ required: true, message: "请输入名称" }],
  "detailConfig.dbType": [{ required: true, message: "请选择数据库类型" }],
  "detailConfig.ip": [{ required: true, message: "请输入Ip地址" }],
  "detailConfig.port": [
    { required: true, message: "请输入端口" },
    { type: "number", message: "端口必须是数字" },
  ],
  "detailConfig.dbName": [{ required: true, message: "请输入数据库名称" }],
  "detailConfig.userName": [{
    required: true,
    message: "请输入用户名",
    validator: (rule, value, callback) => {
      if (formData.value.dataSourceType === DataSourceType.Kafka ||
        formData.value.dataSourceType === DataSourceType.Mqtt) {
        callback();
      } else if (!value) {
        callback(new Error(rule.message as string));
      } else {
        callback();
      }
    }
  }],
  "detailConfig.password": [{
    required: true,
    message: "请输入密码",
    validator: (rule, value, callback) => {
      if (formData.value.dataSourceType === DataSourceType.Kafka ||
        formData.value.dataSourceType === DataSourceType.Mqtt) {
        callback();
      } else if (!value) {
        callback(new Error(rule.message as string));
      } else {
        callback();
      }
    }
  }],
  "detailConfig.url": [{ required: true, message: "请输入连接地址" }],
  "detailConfig.address": [{ required: true, message: "请输入连接地址" }],
  "detailConfig.requestUrl": [{ required: true, message: "请输入请求地址" }],
  "detailConfig.loginUrl": [{
    required: true,
    message: "请输入登录地址",
    validator: (rule, value, callback) => {
      const httpConfig = formData.value.detailConfig as HttpApiConfig;
      if (formData.value.dataSourceType === DataSourceType.HttpApi &&
        httpConfig.isTokenAuthorize === 1 && !value) {
        callback(new Error(rule.message as string));
      } else {
        callback();
      }
    }
  }],
  "detailConfig.intervalTime": [{
    required: true,
    message: "请输入有效时间",
    validator: (rule, value, callback) => {
      const httpConfig = formData.value.detailConfig as HttpApiConfig;
      if (formData.value.dataSourceType === DataSourceType.HttpApi &&
        httpConfig.isTokenAuthorize === 1 && !value) {
        callback(new Error(rule.message as string));
      } else {
        callback();
      }
    }
  }],
  "detailConfig.keyNamePath": [{
    required: true,
    message: "请输入key名称",
    validator: (rule, value, callback) => {
      const httpConfig = formData.value.detailConfig as HttpApiConfig;
      if (formData.value.dataSourceType === DataSourceType.HttpApi &&
        httpConfig.isTokenAuthorize === 1 && !value) {
        callback(new Error(rule.message as string));
      } else {
        callback();
      }
    }
  }],
  "detailConfig.tokenName": [{
    required: true,
    message: "请输入token名称",
    validator: (rule, value, callback) => {
      const httpConfig = formData.value.detailConfig as HttpApiConfig;
      if (formData.value.dataSourceType === DataSourceType.HttpApi &&
        httpConfig.isTokenAuthorize === 1 && !value) {
        callback(new Error(rule.message as string));
      } else {
        callback();
      }
    }
  }],
  remark: [{ required: false }],
  "detailConfig.protocolVersion": [{ required: true, message: "请选择协议版本" }],
});

const isEdit = ref(false);

const visible = defineModel<boolean>();

const { id } = defineProps<{
  id?: number;
}>();

const handleDataSourceTypeChange = (value: DataSourceType) => {
  if (value === DataSourceType.HttpApi) {
    formData.value.detailConfig = {
      isTokenAuthorize: 0,
      headers: {},
      tokenAuthorizeType: TokenAuthorizeType.JWT,
      body: ""
    } as HttpApiConfig;
    headerList.value = [];
  } else if (value === DataSourceType.数据库) {
    formData.value.detailConfig = {
      dbType: DbType.MySql,
      dbName: ""
    };
  } else if (value === DataSourceType.WebSocket) {
    formData.value.detailConfig = {} as WebSocketConfig;
  } else if (value === DataSourceType.Mqtt) {
    formData.value.detailConfig = {} as MqttConfig;
  } else if (value === DataSourceType.Kafka) {
    formData.value.detailConfig = {
      autoOffsetReset: AutoOffsetReset.Earliest
    } as KafkaConfig;
  }
}

const handleSubmit = () => {
  formRef.value?.validate((valid) => {
    if (!valid) {
      return;
    }

    if (isEdit.value) {
      DataSourceService.update(formData.value).then((res) => {
        if (res.success) {
          ElMessage.success("编辑成功");
          visible.value = false;
        }
      });
    } else {
      DataSourceService.add(formData.value).then((res) => {
        if (res.success) {
          ElMessage.success("新增成功");
          visible.value = false;
        }
      });
    }
  });
};

onMounted(() => {
  if (id) {
    isEdit.value = true;
    DataSourceService.getById(id).then((res) => {
      formData.value = res.response;

      // 确保根据数据源类型正确初始化detailConfig
      if (formData.value.dataSourceType === DataSourceType.HttpApi) {
        const httpConfig = formData.value.detailConfig as HttpApiConfig;
        httpConfig.isTokenAuthorize = httpConfig.isTokenAuthorize ?? 0;
        if (!httpConfig.headers) {
          httpConfig.headers = {};
        }
        if (httpConfig.body === undefined) {
          httpConfig.body = "";
        }
        headerList.value = [];
        for (const [key, value] of Object.entries(httpConfig.headers)) {
          headerList.value.push({ key, value });
        }
      }
    });
  } else {
    initFormData();
  }
});
</script>

<style lang="scss" scoped>
.header-list {
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
}

.header-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;

  &:last-child {
    margin-bottom: 0;
  }

  .header-key {
    flex: 1;
  }

  .header-value {
    flex: 1;
  }
}

.add-header {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}

.header-delete-btn {
  flex-shrink: 0;
}

.header-add-btn {
  padding-left: 15px;
  padding-right: 15px;
}
</style>
