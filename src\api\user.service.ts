import { ListQuery, User } from "@/models";
import { http } from "@/utils/http";

export class UserService {
  static baseUrl = "/api/user";
  static authUrl = "/api/auth";

  static getList(query: ListQuery) {
    return http.getList<User>(this.baseUrl, query);
  }

  static add(data: User) {
    return http.post<User>(this.baseUrl, data);
  }

  static update(data: User) {
    return http.put<User>(this.baseUrl, data);
  }

  static delete(id: number) {
    return http.delete(`${this.baseUrl}/${id}`);
  }

  static getById(id: number) {
    return http.get<User>(`${this.baseUrl}/${id}`);
  }

  static login(data: { name: string; pwd: string }) {
    return http.get<User>(this.authUrl, { params: data });
  }
}
