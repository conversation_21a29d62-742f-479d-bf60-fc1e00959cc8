<template>
  <div>
    <el-dialog v-model="visible" :title="isEdit ? '编辑用户' : '新增用户'" width="400px" align-center>
      <el-form :model="formData" :rules="rules" label-width="90px">
        <el-form-item label="用户名称" prop="userDomainName" required>
          <el-input v-model="formData.userDomainName" placeholder="请输入用户名称" />
        </el-form-item>
        <el-form-item label="密码">
          <el-input v-model="formData.passWord" placeholder="请输入密码" show-password />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { UserService } from "@/api";
import { User } from "@/models";
import { ElMessage, FormRules } from "element-plus";
import { onMounted, reactive, ref } from "vue";

defineOptions({
  name: "UserDialog",
});

const visible = defineModel<boolean>();

const { id } = defineProps<{
  id?: number;
}>();

const emit = defineEmits<{
  (e: 'success'): void
}>();

const isEdit = ref(false);

const formData = reactive<User>({
  userDomainName: "",
  passWord: "",
});

const rules = reactive<FormRules>({
  userDomainName: [{ required: true, message: "请输入用户名称" }],
});

function handleSubmit() {
  if (isEdit.value) {
    UserService.update(formData).then((res) => {
      if (res.success) {
        ElMessage.success("编辑成功");
        visible.value = false;
        emit('success');
      }
    });
  } else {
    UserService.add(formData).then((res) => {
      if (res.success) {
        ElMessage.success("新增成功");
        visible.value = false;
        emit('success');
      }
    });
  }
}

onMounted(() => {
  if (id) {
    isEdit.value = true;
    UserService.getById(id).then((res) => {
      Object.assign(formData, res.response);
    });
  }
});
</script>

<style lang="scss" scoped></style>
